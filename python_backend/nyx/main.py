# zero_code/main.py
import asyncio
import os
import time

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from .routers import generate_response


class Nyx:
    def __init__(self):
        self.app = FastAPI(
            title="NYX Makeup AI API",
            description="AI-powered social media response generator",
            version="1.0.0",
        )
        self.setup_middleware()
        self.setup_routers()

    def setup_routers(self):
        self.app.include_router(generate_response.router)

    def setup_middleware(self):
        # Add request timing middleware
        @self.app.middleware("http")
        async def add_process_time_header(request: Request, call_next):
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            return response

        # Add trusted host middleware for security
        self.app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"],  # Configure this properly in production
        )

        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )


def create_app():
    nyx = Nyx()
    return nyx.app
