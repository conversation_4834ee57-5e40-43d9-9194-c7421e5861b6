events {}

http {
  server {
    listen 80;

    # Frontend (React, etc.)
    location / {
      proxy_pass http://frontend:3000;
      proxy_set_header Host $host;
    }

    # Node Backend
    location /api/ {
      proxy_pass http://node-backend:3001/;
      proxy_set_header Host $host;
    }

    # Optional: Python backend (if needed)
    location /python/ {
      proxy_pass http://python-api:8000/;
      proxy_set_header Host $host;
    }

    # Optional: Instagram API (if needed)
    location /instagram/ {
      proxy_pass http://instagram-api:3010/;
      proxy_set_header Host $host;
    }
  }
}
