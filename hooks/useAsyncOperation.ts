import { useCallback, useRef } from 'react';

interface AsyncOperationOptions {
  onStart?: () => void;
  onSuccess?: (result: any) => void;
  onError?: (error: Error) => void;
  onFinally?: () => void;
  operationKey?: string; // Optional key to identify specific operations
}

interface PendingOperation {
  id: string;
  controller: AbortController;
  timestamp: number;
  operationKey?: string;
}

export function useAsyncOperation() {
  const pendingOperationsRef = useRef<Map<string, PendingOperation>>(new Map());

  const execute = useCallback(async <T>(
    operation: (signal: AbortSignal) => Promise<T>,
    options: AsyncOperationOptions = {}
  ): Promise<T | null> => {
    // Generate unique ID for this operation
    const operationId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create new abort controller for this operation
    const controller = new AbortController();
    const signal = controller.signal;

    // Store this operation as pending
    const pendingOp: PendingOperation = {
      id: operationId,
      controller,
      timestamp: Date.now(),
      operationKey: options.operationKey
    };

    pendingOperationsRef.current.set(operationId, pendingOp);

    // Clean up old completed operations (older than 30 seconds)
    const now = Date.now();
    for (const [id, op] of pendingOperationsRef.current.entries()) {
      if (now - op.timestamp > 30000) {
        pendingOperationsRef.current.delete(id);
      }
    }

    try {
      options.onStart?.();

      const result = await operation(signal);

      // Check if operation was cancelled
      if (signal.aborted) {
        return null;
      }

      // Always call onSuccess for completed operations (removed latest operation check)
      options.onSuccess?.(result);

      return result;
    } catch (error) {
      // Don't handle abort errors as real errors
      if (error instanceof Error && error.name === 'AbortError') {
        return null;
      }

      // Always call onError for failed operations (removed latest operation check)
      options.onError?.(error as Error);
      throw error;
    } finally {
      // Clean up this operation
      pendingOperationsRef.current.delete(operationId);

      // Always call onFinally if operation wasn't aborted
      if (!signal.aborted) {
        options.onFinally?.();
      }
    }
  }, []);

  const cancel = useCallback(() => {
    // Cancel all pending operations
    for (const [id, op] of pendingOperationsRef.current.entries()) {
      op.controller.abort();
    }
    pendingOperationsRef.current.clear();
  }, []);

  const cancelByKey = useCallback((operationKey: string) => {
    // Cancel operations with specific key
    for (const [id, op] of pendingOperationsRef.current.entries()) {
      if (op.operationKey === operationKey) {
        op.controller.abort();
        pendingOperationsRef.current.delete(id);
      }
    }
  }, []);

  const getPendingOperations = useCallback(() => {
    return Array.from(pendingOperationsRef.current.values());
  }, []);

  const hasPendingOperation = useCallback((operationKey?: string) => {
    if (!operationKey) {
      return pendingOperationsRef.current.size > 0;
    }
    return Array.from(pendingOperationsRef.current.values()).some(op => op.operationKey === operationKey);
  }, []);

  return {
    execute,
    cancel,
    cancelByKey,
    getPendingOperations,
    hasPendingOperation
  };
}
