import { useCallback, useRef } from 'react';

interface AsyncOperationOptions {
  onStart?: () => void;
  onSuccess?: (result: any) => void;
  onError?: (error: Error) => void;
  onFinally?: () => void;
}

interface PendingOperation {
  id: string;
  controller: AbortController;
  timestamp: number;
  fieldId: string;
}

export function useFieldAsyncOperations() {
  const pendingOperationsRef = useRef<Map<string, PendingOperation>>(new Map());

  const execute = useCallback(async <T>(
    fieldId: string,
    operation: (signal: AbortSignal) => Promise<T>,
    options: AsyncOperationOptions = {}
  ): Promise<T | null> => {
    // Cancel any existing operation for this field
    const existingOps = Array.from(pendingOperationsRef.current.values())
      .filter(op => op.fieldId === fieldId);
    
    for (const op of existingOps) {
      op.controller.abort();
      pendingOperationsRef.current.delete(op.id);
    }

    // Generate unique ID for this operation
    const operationId = `${fieldId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create new abort controller for this operation
    const controller = new AbortController();
    const signal = controller.signal;

    // Store this operation as pending
    const pendingOp: PendingOperation = {
      id: operationId,
      controller,
      timestamp: Date.now(),
      fieldId
    };

    pendingOperationsRef.current.set(operationId, pendingOp);

    // Clean up old completed operations (older than 30 seconds)
    const now = Date.now();
    for (const [id, op] of pendingOperationsRef.current.entries()) {
      if (now - op.timestamp > 30000) {
        pendingOperationsRef.current.delete(id);
      }
    }

    try {
      options.onStart?.();

      const result = await operation(signal);

      // Check if operation was cancelled
      if (signal.aborted) {
        return null;
      }

      // Call onSuccess for completed operations
      options.onSuccess?.(result);

      return result;
    } catch (error) {
      // Don't handle abort errors as real errors
      if (error instanceof Error && error.name === 'AbortError') {
        return null;
      }

      // Call onError for failed operations
      options.onError?.(error as Error);
      throw error;
    } finally {
      // Clean up this operation
      pendingOperationsRef.current.delete(operationId);

      // Call onFinally if operation wasn't aborted
      if (!signal.aborted) {
        options.onFinally?.();
      }
    }
  }, []);

  const cancelField = useCallback((fieldId: string) => {
    // Cancel all operations for a specific field
    const opsToCancel = Array.from(pendingOperationsRef.current.values())
      .filter(op => op.fieldId === fieldId);
    
    for (const op of opsToCancel) {
      op.controller.abort();
      pendingOperationsRef.current.delete(op.id);
    }
  }, []);

  const cancelAll = useCallback(() => {
    // Cancel all pending operations
    for (const [id, op] of pendingOperationsRef.current.entries()) {
      op.controller.abort();
    }
    pendingOperationsRef.current.clear();
  }, []);

  const isFieldLoading = useCallback((fieldId: string) => {
    return Array.from(pendingOperationsRef.current.values())
      .some(op => op.fieldId === fieldId);
  }, []);

  const getPendingOperations = useCallback(() => {
    return Array.from(pendingOperationsRef.current.values());
  }, []);

  return { 
    execute, 
    cancelField, 
    cancelAll, 
    isFieldLoading,
    getPendingOperations 
  };
}
