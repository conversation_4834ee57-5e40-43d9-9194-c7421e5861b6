"use client"

import { useState, useEffect, useCallback } from "react"
import { useFieldAsyncOperations } from "../hooks/useFieldAsyncOperations"
import { twitterApi, tiktok<PERSON>pi, instagram<PERSON>pi, ApiError } from "../utils/api"

// Define TypeScript interfaces for our data structures
interface TwitterField {
  id: string
  value: string
  isLoading: boolean
  responses: string[] | null
  activeResponse?: number
}

interface CopyStatusState {
  [key: string]: boolean
}

export default function Home() {
  const [twitterFields, setTwitterFields] = useState<TwitterField[]>([
    {
      id: "1",
      value: "",
      isLoading: false,
      responses: null,
    },
  ])

  const [copyStatus, setCopyStatus] = useState<CopyStatusState>({})
  const [darkMode, setDarkMode] = useState<boolean>(false)

  const [tiktokFields, setTiktokFields] = useState<TwitterField[]>([
    {
      id: Date.now().toString(),
      value: "",
      isLoading: false,
      responses: null,
    },
  ])
const [instagramFields, setInstagramFields] = useState<TwitterField[]>([
  {
    id: Date.now().toString(),
    value: "",
    isLoading: false,
    responses: null,
  },
])

  // Check system preference for dark mode
  useEffect(() => {
    const darkModeMediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    setDarkMode(darkModeMediaQuery.matches)

    const handler = (e: MediaQueryListEvent) => setDarkMode(e.matches)
    darkModeMediaQuery.addEventListener("change", handler)

    return () => darkModeMediaQuery.removeEventListener("change", handler)
  }, [])

  // Animate response containers when they appear
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("visible")
          }
        })
      },
      { threshold: 0.1 },
    )

    document.querySelectorAll(".response-container").forEach((el) => {
      observer.observe(el)
    })

    return () => {
      document.querySelectorAll(".response-container").forEach((el) => {
        observer.unobserve(el)
      })
    }
  }, [twitterFields])

  const addField = (): void => {
    setTwitterFields([
      ...twitterFields,
      {
        id: Date.now().toString(),
        value: "",
        isLoading: false,
        responses: null,
      },
    ])
  }

  const updateFieldValue = (id: string, value: string): void => {
    setTwitterFields(twitterFields.map((field) => (field.id === id ? { ...field, value } : field)))
  }

  const deleteField = (id: string): void => {
    setTwitterFields(twitterFields.filter((field) => field.id !== id))
  }

  const clearAllFields = (): void => {
    setTwitterFields([
      {
        id: Date.now().toString(),
        value: "",
        isLoading: false,
        responses: null,
      },
    ])
  }

  // --- NEW: Function to clear responses for a single field ---
  const clearResponses = (id: string): void => {
    setTwitterFields(
      twitterFields.map((field) =>
        field.id === id
          ? { ...field, responses: null, isLoading: false, activeResponse: undefined } // Reset responses and loading state
          : field,
      ),
    );
  };
  // --- End NEW ---

  const fieldAsyncOps = useFieldAsyncOperations();

  const runAnalysis = useCallback(async (id: string): Promise<void> => {
    const fieldToUpdate = twitterFields.find((field) => field.id === id);
    if (!fieldToUpdate) return;

    // Check if field is already loading to prevent duplicate requests
    if (fieldAsyncOps.isFieldLoading(id)) {
      console.log(`Twitter analysis already in progress for field ${id}, skipping duplicate request`);
      return;
    }

    await fieldAsyncOps.execute(
      id,
      async (signal: AbortSignal) => {
        // Generate unique request ID for tracking
        const requestId = `twitter-${id}-${Date.now()}`;

        const data = await twitterApi.processTweet(fieldToUpdate.value, signal, requestId);
        return { responses: data.response || [], requestId };
      },
      {
        onStart: () => {
          // Set loading state immediately when starting
          setTwitterFields(prev => prev.map((field) =>
            field.id === id ? { ...field, isLoading: true, responses: null } : field
          ));
        },
        onSuccess: (result: { responses: string[], requestId: string }) => {
          console.log(`Twitter analysis completed for request ${result.requestId}`);
          setTwitterFields(prev => prev.map((field) =>
            field.id === id
              ? {
                  ...field,
                  isLoading: false,
                  responses: result.responses.length > 0 ? result.responses : ['No analysis generated.'],
                  activeResponse: 0,
                }
              : field,
          ));
        },
        onError: (error: Error) => {
          console.error("Failed to run analysis:", error);
          const errorMessage = error instanceof ApiError
            ? `Error: ${error.message}`
            : "Error: Network error or backend unavailable.";

          setTwitterFields(prev => prev.map((field) =>
            field.id === id
              ? {
                  ...field,
                  isLoading: false,
                  responses: [errorMessage],
                }
              : field,
          ));
        }
      }
    );
  }, [twitterFields, fieldAsyncOps]);

  const switchResponse = (fieldId: string, responseIndex: number): void => {
    setTwitterFields(
      twitterFields.map((field) => (field.id === fieldId ? { ...field, activeResponse: responseIndex } : field)),
    )
  }

  const copyToClipboard = async (fieldId: string, responseIndex: number): Promise<void> => {
    const field = twitterFields.find((f) => f.id === fieldId)
    if (field && field.responses) {
      try {
        await navigator.clipboard.writeText(field.responses[responseIndex])

        // Set copy status for this specific field/response
        setCopyStatus({
          ...copyStatus,
          [`${fieldId}-${responseIndex}`]: true,
        })

        // Reset copy status after 2 seconds
        setTimeout(() => {
          setCopyStatus({
            ...copyStatus,
            [`${fieldId}-${responseIndex}`]: false,
          })
        }, 2000)
      } catch (err) {
        console.error("Failed to copy text: ", err)
      }
    }
  }

  // ADD
  const addTikTokField = (): void => {
    setTiktokFields([
      ...tiktokFields,
      {
        id: Date.now().toString(),
        value: "",
        isLoading: false,
        responses: null,
      },
    ])
  }

  // UPDATE
  const updateTikTokFieldValue = (id: string, value: string): void => {
    setTiktokFields(
      tiktokFields.map((f) => (f.id === id ? { ...f, value } : f))
    )
  }

  // DELETE
  const deleteTikTokField = (id: string): void => {
    setTiktokFields(tiktokFields.filter((f) => f.id !== id))
  }

  // CLEAR ALL
  const clearAllTikTokFields = (): void => {
    setTiktokFields([
      {
        id: Date.now().toString(),
        value: "",
        isLoading: false,
        responses: null,
      },
    ])
  }

  // CLEAR RESPONSES
  const clearTikTokResponses = (id: string): void => {
    setTiktokFields(
      tiktokFields.map((f) =>
        f.id === id
          ? { ...f, responses: null, isLoading: false, activeResponse: undefined }
          : f
      )
    )
  }

  // RUN ANALYSIS
  const runTikTokAnalysis = useCallback(async (id: string): Promise<void> => {
    const field = tiktokFields.find((f) => f.id === id);
    if (!field) return;

    // Check if field is already loading to prevent duplicate requests
    if (fieldAsyncOps.isFieldLoading(id)) {
      console.log(`TikTok analysis already in progress for field ${id}, skipping duplicate request`);
      return;
    }

    await fieldAsyncOps.execute(
      id,
      async (signal: AbortSignal) => {
        // Generate unique request ID for tracking
        const requestId = `tiktok-${id}-${Date.now()}`;

        const data = await tiktokApi.generateResponse(field.value, signal, requestId);
        return { responses: data.response || [], requestId };
      },
      {
        onStart: () => {
          // Set loading state immediately when starting
          setTiktokFields(prev =>
            prev.map((f) =>
              f.id === id ? { ...f, isLoading: true, responses: null } : f
            )
          );
        },
        onSuccess: (result: { responses: string[], requestId: string }) => {
          console.log(`TikTok analysis completed for request ${result.requestId}`);
          setTiktokFields(prev =>
            prev.map((f) =>
              f.id === id
                ? {
                    ...f,
                    isLoading: false,
                    responses: result.responses.length ? result.responses : ["No analysis generated."],
                    activeResponse: 0,
                  }
                : f
            )
          );
        },
        onError: (error: Error) => {
          console.error("TikTok analysis error:", error);
          const errorMessage = error instanceof ApiError
            ? `Error: ${error.message}`
            : "Error: Network or backend unavailable.";

          setTiktokFields(prev =>
            prev.map((f) =>
              f.id === id
                ? {
                    ...f,
                    isLoading: false,
                    responses: [errorMessage],
                  }
                : f
            )
          );
        }
      }
    );
  }, [tiktokFields, fieldAsyncOps]);

  // SWITCH RESPONSE TAB
  const switchTikTokResponse = (fieldId: string, idx: number): void => {
    setTiktokFields(
      tiktokFields.map((f) =>
        f.id === fieldId ? { ...f, activeResponse: idx } : f
      )
    )
  }

  // COPY TO CLIPBOARD
  const copyTikTokToClipboard = async (
    fieldId: string,
    responseIndex: number
  ): Promise<void> => {
    const field = tiktokFields.find((f) => f.id === fieldId)
    if (!field?.responses) return

    try {
      await navigator.clipboard.writeText(field.responses[responseIndex])
      setCopyStatus({
        ...copyStatus,
        [`tiktok-${fieldId}-${responseIndex}`]: true,
      })
      setTimeout(() => {
        setCopyStatus({
          ...copyStatus,
          [`tiktok-${fieldId}-${responseIndex}`]: false,
        })
      }, 2000)
    } catch (e) {
      console.error("Copy failed", e)
    }
  }

// ADD
const addInstagramField = (): void => {
  setInstagramFields([
    ...instagramFields,
    {
      id: Date.now().toString(),
      value: "",
      isLoading: false,
      responses: null,
    },
  ])
}

// UPDATE
const updateInstagramFieldValue = (id: string, value: string): void => {
  setInstagramFields(
    instagramFields.map((f) => (f.id === id ? { ...f, value } : f))
  )
}

// DELETE
const deleteInstagramField = (id: string): void => {
  setInstagramFields(instagramFields.filter((f) => f.id !== id))
}

// CLEAR ALL
const clearAllInstagramFields = (): void => {
  setInstagramFields([
    {
      id: Date.now().toString(),
      value: "",
      isLoading: false,
      responses: null,
    },
  ])
}

// CLEAR RESPONSES
const clearInstagramResponses = (id: string): void => {
  setInstagramFields(
    instagramFields.map((f) =>
      f.id === id
        ? { ...f, responses: null, isLoading: false, activeResponse: undefined }
        : f
    )
  )
}

// RUN ANALYSIS
const runInstagramAnalysis = useCallback(async (id: string): Promise<void> => {
  const field = instagramFields.find((f) => f.id === id);
  if (!field) return;

  // Check if field is already loading to prevent duplicate requests
  if (fieldAsyncOps.isFieldLoading(id)) {
    console.log(`Instagram analysis already in progress for field ${id}, skipping duplicate request`);
    return;
  }

  await fieldAsyncOps.execute(
    id,
    async (signal: AbortSignal) => {
      // Generate unique request ID for tracking
      const requestId = `instagram-${id}-${Date.now()}`;

      const url = field.value?.trim();
      const match = url?.match(/\/(?:reel|p)\/([^/?#]+)/);
      const shortcode = match?.[1];

      if (!shortcode) {
        throw new Error("Invalid Instagram post or reel URL.");
      }

      const postData = await instagramApi.getPost(shortcode, signal, requestId);

      if (postData.isPhoto) {
        const photoResponse = await instagramApi.generatePhotoResponse(postData, signal, requestId);
        return {
          responses: photoResponse.response || [],
          requestId,
        };
      } else {
        const downloadData = await instagramApi.downloadVideo(
          postData.data.xdt_shortcode_media.video_url,
          `${shortcode}.mp4`,
          signal,
          requestId
        );
        const videoResponse = await instagramApi.generateVideoResponse(
          downloadData.path,
          postData.data.xdt_shortcode_media.coauthor_producers,
          postData.data.xdt_shortcode_media.edge_media_to_caption,
          signal,
          requestId
        );
        return {
          responses: videoResponse.response || [],
          coauthor_producers: postData.data.xdt_shortcode_media.coauthor_producers,
          edge_media_to_caption: postData.data.xdt_shortcode_media.edge_media_to_caption
        };
      }
    },
    {
      onStart: () => {
        // Set loading state immediately when starting
        setInstagramFields(prev =>
          prev.map((f) =>
            f.id === id ? { ...f, isLoading: true, responses: null } : f
          )
        );
      },
      onSuccess: (result: { responses: string[], requestId: string, coauthor_producers: string[], edge_media_to_caption: string[] }) => {
        console.log(`Instagram analysis completed for request ${result.requestId}`);
        setInstagramFields(prev =>
          prev.map((f) =>
            f.id === id
              ? {
                  ...f,
                  isLoading: false,
                  responses: result.responses.length ? result.responses : ["No analysis generated."],
                  coauthor_producers: result.coauthor_producers,
                  edge_media_to_caption: result.edge_media_to_caption,
                  activeResponse: 0,
                }
              : f
          )
        );
      },
      onError: (error: Error) => {
        console.error("Instagram analysis error:", error);
        const errorMessage = error instanceof ApiError
          ? `Error: ${error.message}`
          : `Error: ${error.message}`;

        setInstagramFields(prev =>
          prev.map((f) =>
            f.id === id
              ? {
                  ...f,
                  isLoading: false,
                  responses: [errorMessage],
                }
              : f
          )
        );
      }
    }
  );
}, [instagramFields, fieldAsyncOps]);





// SWITCH RESPONSE TAB
const switchInstagramResponse = (fieldId: string, idx: number): void => {
  setInstagramFields(
    instagramFields.map((f) =>
      f.id === fieldId ? { ...f, activeResponse: idx } : f
    )
  )
}

// COPY TO CLIPBOARD
const copyInstagramToClipboard = async (
  fieldId: string,
  responseIndex: number
): Promise<void> => {
  const field = instagramFields.find((f) => f.id === fieldId)
  if (!field?.responses) return

  try {
    await navigator.clipboard.writeText(field.responses[responseIndex])
    setCopyStatus({
      ...copyStatus,
      [`instagram-${fieldId}-${responseIndex}`]: true,
    })
    setTimeout(() => {
      setCopyStatus({
        ...copyStatus,
        [`instagram-${fieldId}-${responseIndex}`]: false,
      })
    }, 2000)
  } catch (e) {
    console.error("Copy failed", e)
  }
}

// ...existing code...

  // Function to render engagement bars

  return (
    <main className="main-container">
      <div className="container">
        <header className="header">
          <h1 className="title">NYX Professional Makeup</h1>
          <p className="subtitle"></p>
        </header>

        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Twitter ID</h2>
            <div className="button-group">
              <button onClick={clearAllFields} className="clear-button">
                <svg
                  className="clear-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
                Clear All
              </button>
              <button onClick={addField} className="add-button">
                <svg
                  className="plus-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                Add Field
              </button>
            </div>
          </div>

          <div className="fields-container">
            {twitterFields.map((field) => (
              <div key={field.id} className="field-container">
                <div className="field-row">
                  <div className="input-container">
                    <input
                      type="text"
                      placeholder="Enter Twitter ID"
                      value={field.value}
                      onChange={(e) => updateFieldValue(field.id, e.target.value)}
                      className="text-input"
                    />
                  </div>

                  {!field.isLoading && !field.responses && (
                    <>
                      <button onClick={() => runAnalysis(field.id)} disabled={!field.value} className="run-button">
                        Run
                      </button>
                      <button onClick={() => deleteField(field.id)} className="delete-button">
                        <svg
                          className="delete-icon"
                          xmlns="http://www.w3.org/2000/svg"
                          width="18"
                          height="18"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M3 6h18"></path>
                          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                      </button>
                    </>
                  )}

                  {field.isLoading && (
                    <div className="loading-container">
                      <button disabled className="run-button loading">
                        <span className="loader"></span>
                        Running
                      </button>
                      <button
                        onClick={() => {
                          fieldAsyncOps.cancelField(field.id);
                          deleteField(field.id);
                        }}
                        className="cancel-button"
                        title="Cancel Request"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </button>
                    </div>
                  )}

                  {field.responses && (
                    <>
                      {/* --- NEW: Clear Responses Button --- */}
                      <button 
                        onClick={() => clearResponses(field.id)} 
                        className="clear-responses-button delete-button" 
                        title="Clear Responses"
                      >
                        <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          width="18" 
                          height="18" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                         >
                           <polyline points="23 4 23 10 17 10"></polyline>
                           <polyline points="1 4 1 10 7 10"></polyline>
                           <path d="M3.51 9a9 9 0 0 1 14.85-3.36L20.49 9"></path>
                           <path d="M20.49 15a9 9 0 0 1-14.85 3.36L3.51 15"></path>
                         </svg>
                       </button>
                       {/* --- End NEW --- */}

                       <button onClick={() => deleteField(field.id)} className="delete-button" title="Delete Field">
                         <svg
                           className="delete-icon"
                           xmlns="http://www.w3.org/2000/svg"
                           width="18"
                           height="18"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           strokeWidth="2"
                           strokeLinecap="round"
                           strokeLinejoin="round"
                         >
                           <path d="M3 6h18"></path>
                           <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                           <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                         </svg>
                       </button>
                    </>
                  )}
                </div>

                {field.responses && (
                  <div className="response-container">
                    <div className="response-header">
                      <div className="response-tabs">
                        <button
                          className={`tab-button ${field.activeResponse === 0 ? "active" : ""}`}
                          onClick={() => switchResponse(field.id, 0)}
                        >
                          Response
                        </button>
                        <button
                          className={`tab-button ${field.activeResponse === 1 ? "active" : ""}`}
                          onClick={() => switchResponse(field.id, 1)}
                        >
                          Response
                        </button>
                        <button
                          className={`tab-button ${field.activeResponse === 2 ? "active" : ""}`}
                          onClick={() => switchResponse(field.id, 2)}
                        >
                          Response
                        </button>
                      </div>
                      <button
                        onClick={() => copyToClipboard(field.id, field.activeResponse || 0)}
                        className={`copy-button ${copyStatus[`${field.id}-${field.activeResponse}`] ? "copied" : ""}`}
                      >
                        {copyStatus[`${field.id}-${field.activeResponse}`] ? (
                          <>
                            <svg
                              className="check-icon"
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                            Copied!
                          </>
                        ) : (
                          <>
                            <svg
                              className="copy-icon"
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                            Copy
                          </>
                        )}
                      </button>
                    </div>

                    {/* Enhanced response content with visual elements */}
                    <div className="response-content">
                      {field.responses[field.activeResponse || 0].split("\n").map((line, index) => (
                        <p key={index}>{line}</p>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
    <div className="card">
    <div className="card-header">
      <h2 className="card-title">TikTok Video Link</h2>
      <div className="button-group">
        <button
          onClick={clearAllTikTokFields}
          className="clear-button"
        >
                          <svg
                  className="clear-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg> Clear All
        </button>
        <button onClick={addTikTokField} className="add-button">
        <svg
                  className="plus-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg> Add Field
        </button>
      </div>
    </div>

    <div className="fields-container">
      {tiktokFields.map((field) => (
        <div key={field.id} className="field-container">
          <div className="field-row">
            <div className="input-container">
              <input
                type="text"
                placeholder="Enter TikTok ID"
                value={field.value}
                onChange={(e) =>
                  updateTikTokFieldValue(field.id, e.target.value)
                }
                className="text-input"
              />
            </div>

            {!field.isLoading && !field.responses && (
              <>
                <button
                  onClick={() => runTikTokAnalysis(field.id)}
                  disabled={!field.value}
                  className="run-button"
                >
                  Run
                </button>
                <button
                  onClick={() => deleteTikTokField(field.id)}
                  className="delete-button"
                >
                  <svg
                           className="delete-icon"
                           xmlns="http://www.w3.org/2000/svg"
                           width="18"
                           height="18"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           strokeWidth="2"
                           strokeLinecap="round"
                           strokeLinejoin="round"
                         >
                           <path d="M3 6h18"></path>
                           <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                           <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                         </svg>
                </button>
              </>
            )}

            {field.isLoading && (
              <div className="loading-container">
                <button disabled className="run-button loading">
                  <span className="loader"></span> Running
                </button>
                <button
                  onClick={() => {
                    fieldAsyncOps.cancelField(field.id);
                    deleteTikTokField(field.id);
                  }}
                  className="cancel-button"
                  title="Cancel Request"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
            )}

            {field.responses && (
              <>
                <button
                  onClick={() => clearTikTokResponses(field.id)}
                  className="clear-responses-button delete-button"
                  title="Clear Responses"
                >
                  <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          width="18" 
                          height="18" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                         >
                           <polyline points="23 4 23 10 17 10"></polyline>
                           <polyline points="1 4 1 10 7 10"></polyline>
                           <path d="M3.51 9a9 9 0 0 1 14.85-3.36L20.49 9"></path>
                           <path d="M20.49 15a9 9 0 0 1-14.85 3.36L3.51 15"></path>
                         </svg>
                </button>
                <button
                  onClick={() => deleteTikTokField(field.id)}
                  className="delete-button"
                  title="Delete Field"
                >
                 <svg
                           className="delete-icon"
                           xmlns="http://www.w3.org/2000/svg"
                           width="18"
                           height="18"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           strokeWidth="2"
                           strokeLinecap="round"
                           strokeLinejoin="round"
                         >
                           <path d="M3 6h18"></path>
                           <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                           <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                         </svg>
                </button>
              </>
            )}
          </div>

          {field.responses && (
            <div className="response-container">
              <div className="response-header">
                <div className="response-tabs">
                  {field.responses.map((_, idx) => (
                    <button
                      key={idx}
                      className={`tab-button ${
                        field.activeResponse === idx ? "active" : ""
                      }`}
                      onClick={() =>
                        switchTikTokResponse(field.id, idx)
                      }
                    >
                      Response {idx + 1}
                    </button>
                  ))}
                </div>
                <button
                  onClick={() =>
                    copyTikTokToClipboard(
                      field.id,
                      field.activeResponse || 0
                    )
                  }
                  className={`copy-button ${
                    copyStatus[
                      `tiktok-${field.id}-${
                        field.activeResponse || 0
                      }`
                    ]
                      ? "copied"
                      : ""
                  }`}
                >
                  {copyStatus[
                    `tiktok-${field.id}-${
                      field.activeResponse || 0
                    }`
                  ] ? (
                    <>
                      <svg
                              className="check-icon"
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg> Copied!
                    </>
                  ) : (
                    <>
                      <svg
                              className="copy-icon"
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg> Copy
                    </>
                  )}
                </button>
              </div>

              <div className="response-content">
                {field.responses[
                  field.activeResponse || 0
                ]
                  .split("\n")
                  .map((line, i) => (
                    <p key={i}>{line}</p>
                  ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  </div>
  <div className="card">
  <div className="card-header">
    <h2 className="card-title">Instagram Post Link</h2>
    <div className="button-group">
      <button onClick={clearAllInstagramFields} className="clear-button">
        <svg
          className="clear-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M3 6h18"></path>
          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
          <line x1="10" y1="11" x2="10" y2="17"></line>
          <line x1="14" y1="11" x2="14" y2="17"></line>
        </svg>
        Clear All
      </button>
      <button onClick={addInstagramField} className="add-button">
        <svg
          className="plus-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="16"></line>
          <line x1="8" y1="12" x2="16" y2="12"></line>
        </svg>
        Add Field
      </button>
    </div>
  </div>

  <div className="fields-container">
    {instagramFields.map((field) => (
      <div key={field.id} className="field-container">
        <div className="field-row">
          <div className="input-container">
            <input
              type="text"
              placeholder="Enter Instagram Post Link"
              value={field.value}
              onChange={(e) => updateInstagramFieldValue(field.id, e.target.value)}
              className="text-input"
            />
          </div>

          {!field.isLoading && !field.responses && (
            <>
              <button
                onClick={() => runInstagramAnalysis(field.id)}
                disabled={!field.value}
                className="run-button"
              >
                Run
              </button>
              <button
                onClick={() => deleteInstagramField(field.id)}
                className="delete-button"
              >
                <svg
                  className="delete-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                </svg>
              </button>
            </>
          )}

          {field.isLoading && (
            <div className="loading-container">
              <button disabled className="run-button loading">
                <span className="loader"></span>
                Running
              </button>
              <button
                onClick={() => {
                  fieldAsyncOps.cancelField(field.id);
                  deleteInstagramField(field.id);
                }}
                className="cancel-button"
                title="Cancel Request"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          )}

          {field.responses && (
            <>
              <button
                onClick={() => clearInstagramResponses(field.id)}
                className="clear-responses-button delete-button"
                title="Clear Responses"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="23 4 23 10 17 10"></polyline>
                  <polyline points="1 4 1 10 7 10"></polyline>
                  <path d="M3.51 9a9 9 0 0 1 14.85-3.36L20.49 9"></path>
                  <path d="M20.49 15a9 9 0 0 1-14.85 3.36L3.51 15"></path>
                </svg>
              </button>
              <button
                onClick={() => deleteInstagramField(field.id)}
                className="delete-button"
                title="Delete Field"
              >
                <svg
                  className="delete-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                </svg>
              </button>
            </>
          )}
        </div>

        {field.responses && (
          <div className="response-container">
            <div className="response-header">
              <div className="response-tabs">
                {field.responses.map((_, idx) => (
                  <button
                    key={idx}
                    className={`tab-button ${field.activeResponse === idx ? "active" : ""}`}
                    onClick={() => switchInstagramResponse(field.id, idx)}
                  >
                    Response {idx + 1}
                  </button>
                ))}
              </div>
              <button
                onClick={() =>
                  copyInstagramToClipboard(field.id, field.activeResponse || 0)
                }
                className={`copy-button ${
                  copyStatus[
                    `instagram-${field.id}-${field.activeResponse || 0}`
                  ]
                    ? "copied"
                    : ""
                }`}
              >
                {copyStatus[
                  `instagram-${field.id}-${field.activeResponse || 0}`
                ] ? (
                  <>
                    <svg
                      className="check-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    Copied!
                  </>
                ) : (
                  <>
                    <svg
                      className="copy-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                    Copy
                  </>
                )}
              </button>
            </div>
            <div className="response-content">
              {field.responses[field.activeResponse || 0]
                .split("\n")
                .map((line, i) => (
                  <p key={i}>{line}</p>
                ))}
            </div>
          </div>
        )}
      </div>
    ))}
  </div>
</div>
      </div>

      {/* Floating action button for adding new fields */}
      <button onClick={addField} className="floating-add-button">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      </button>
    </main>
  )
}

